#!/usr/bin/env python3

import os
import json
import re
import zipfile
import requests
import tabula
import pandas as pd
from datetime import datetime
from PyPDF2 import PdfReader

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

DEFAULT_API_URL = "https://bonum.in/boatsai/insert_invoice_data.php"  # API endpoint for invoice data
DEFAULT_API_KEY = "your-api-key"  # Replace with actual API key
DEFAULT_API_TIMEOUT = 30  # Timeout in seconds

def extract_product_title_with_tabula(pdf_path, page_num):
    """Extract product title using tabula-py for better table structure recognition"""
    print(f"   🔍 Using tabula-py to extract product title from page {page_num}...")

    try:
        # Try different extraction methods
        all_tables = []

        # Method 1: Lattice extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, lattice=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 2: Stream extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, stream=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 3: Guess extraction
        try:
            tables = tabula.read_pdf(pdf_path, pages=page_num, multiple_tables=True, guess=True)
            all_tables.extend(tables)
        except:
            pass

        # Method 4: Extract entire page
        try:
            entire_page = tabula.read_pdf(pdf_path, pages=page_num, pandas_options={'header': None})
            if entire_page:
                all_tables.extend(entire_page)
        except:
            pass

        # Process extracted tables to find product titles
        for df in all_tables:
            if df.empty:
                continue

            # Look for product titles in table data
            product_title = extract_product_from_dataframe(df)
            if product_title:
                print(f"   ✅ Found product title with tabula-py: {product_title}")
                return product_title

        print("   ⚠️ No product title found with tabula-py")
        return None

    except Exception as e:
        print(f"   ❌ Error using tabula-py: {str(e)}")
        return None

def extract_product_from_dataframe(df):
    """Extract product title from a pandas DataFrame"""
    try:
        # Convert all data to string and combine
        all_text = ""
        for col in df.columns:
            for val in df[col].dropna():
                if pd.notna(val):
                    all_text += str(val) + " "

        # Look for product patterns in the combined text
        product_patterns = [
            # Product name followed by HSN/SAC
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+(?:HSN|SAC)[\s:]*\d+',
            # Product name in table row with quantities
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+\d+\s+[\d.]+',
            # Product name followed by IGST percentage
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{10,}?)\s+IGST[\s:]*\d+',
            # General product pattern
            r'([A-Za-z][A-Za-z0-9\s\-\(\)\.\/]{15,}?)(?=\s+(?:\d+|HSN|SAC|IGST))',
        ]

        for pattern in product_patterns:
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                candidate = match.group(1).strip()
                cleaned = clean_product_title(candidate)
                if is_valid_product_title(cleaned) and len(cleaned) > 10:
                    return cleaned

        # Look for product titles in individual cells
        for col in df.columns:
            for val in df[col].dropna():
                if pd.notna(val):
                    val_str = str(val).strip()
                    if len(val_str) > 15 and is_valid_product_title(val_str):
                        cleaned = clean_product_title(val_str)
                        if cleaned and len(cleaned) > 10:
                            return cleaned

        return None

    except Exception as e:
        print(f"   ❌ Error processing DataFrame: {str(e)}")
        return None

def clean_product_title(title):
    """Clean and format product title"""
    if not title:
        return ""

    title = title.strip()

    # Remove common suffixes and patterns
    title = re.sub(r'\s*\d+\s*(?:Rs\.?|₹)?\s*\d*\.?\d*$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\(Pack of \d+\)$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*\b\d{4,}\b$', '', title)  # Remove trailing large numbers
    title = re.sub(r'\s*(HSN|SAC|SKU|FSN|Model|Code):?\s*[A-Za-z0-9-]+$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s*(IGST|CGST|SGST)[\s:]*\d+\.?\d*\s*%?$', '', title, flags=re.IGNORECASE)
    title = re.sub(r'\s+', ' ', title).strip()
    title = re.sub(r'[^a-zA-Z\s]$', '', title)

    return title

def is_valid_product_title(title):
    """Check if the extracted text is a valid product title"""
    if not title:
        return False
    if len(title) < 3:
        return False
    if not re.search(r'[a-zA-Z]', title):
        return False

    # Skip obvious non-product patterns
    invalid_patterns = [
        r'^(Rs\.?|₹)?\s*\d+\.?\d*$',
        r'^\d+\s*(pcs|units|qty|piece|pieces)?$',
        r'^(HSN|SAC|SKU|FSN|IMEI|PAN|GSTIN|CIN|IRN)[:\s]*[A-Za-z0-9]*$',
        r'^(Contact|Phone|Email|Address|Website).*$',
        r'^(Total|Grand|Sub|Net|Final)\s*(Amount|Price|Cost|Value).*$',
        r'^(Tax|GST|IGST|SGST|CGST|VAT).*$',
        r'^(Qty|Quantity|Gross|Discount|Taxable).*$',
        r'^(Invoice|Bill|Receipt|Order).*$',
        r'^(Date|Time|Number|No\.?).*$',
        r'^[A-Z]{2,}\d+[A-Z]*$',
        r'^\d{2}-\d{2}-\d{4}$',
        r'^[A-Z0-9]{10,}$',
    ]

    for pattern in invalid_patterns:
        if re.match(pattern, title, re.IGNORECASE):
            return False

    # Check for non-product words
    non_product_words = {
        'contact', 'flipkart', 'amazon', 'seller', 'buyer', 'customer', 'company',
        'address', 'phone', 'email', 'website', 'total', 'amount', 'price', 'cost',
        'tax', 'gst', 'igst', 'sgst', 'cgst', 'hsn', 'sac', 'qty', 'quantity',
        'gross', 'discount', 'taxable', 'invoice', 'bill', 'receipt', 'order',
        'date', 'time', 'number', 'signature', 'authorized', 'signatory', 'imei', 'serial'
    }

    words = title.lower().split()
    if len(words) > 0:
        non_product_count = sum(1 for word in words if word in non_product_words)
        if non_product_count / len(words) > 0.7:
            return False

    # Skip table headers
    table_headers = [
        'title', 'qty', 'gross', 'discount', 'taxable', 'igst', 'sgst', 'total',
        'amount', 'price', 'rate', 'value', 'description', 'item', 'product'
    ]

    if title.lower().strip() in table_headers:
        return False

    if len(title) > 100:
        return False

    return True

def extract_product_title_enhanced(text, pdf_path, page_num):
    """Enhanced product title extraction using both PyPDF2 and tabula-py"""
    print(f"   🔍 Enhanced product title extraction for page {page_num}...")

    # First try tabula-py for better table structure recognition
    tabula_result = extract_product_title_with_tabula(pdf_path, page_num)
    if tabula_result:
        return tabula_result

    # Fallback to original text-based extraction
    print("   📝 Falling back to text-based extraction...")
    return extract_product_title_original(text)

def extract_product_title_original(text):
    """Original product title extraction logic from your code"""
    if not text:
        return ""

    # Your existing product title extraction logic here
    # (I'll include the key parts from your original functions)

    # Try vertical scanning first
    lines = text.split('\n')
    product_headers = [
        'description', 'product', 'title', 'item', 'name', 'goods',
        'product title', 'product name', 'item description', 'description of goods',
        'product description', 'item name', 'commodity', 'article', 'details'
    ]

    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        for header in product_headers:
            if (header in line_lower and len(line_lower) < 100):
                # Look for product title in next few lines
                for j in range(i + 1, min(i + 10, len(lines))):
                    if j >= len(lines):
                        break

                    candidate_line = lines[j].strip()
                    if not candidate_line:
                        continue

                    # Skip unwanted patterns
                    skip_words = ['hsn', 'sac', 'qty', 'gross', 'discount', 'taxable', 'igst', 'total']
                    if any(skip_word in candidate_line.lower() for skip_word in skip_words):
                        continue

                    if (re.search(r'[A-Za-z]', candidate_line) and
                        len(candidate_line) > 10 and
                        not re.search(r'^\d+\s*%', candidate_line)):

                        cleaned_candidate = clean_product_title(candidate_line)
                        if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                            return cleaned_candidate

    # Try table pattern matching
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if re.search(r'^[A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?\s+\d+\s+[\d.]+', line_stripped):
            product_match = re.match(r'^([A-Za-z][A-Za-z0-9\s\-\(\)\.]{10,}?)\s+\d+\s+[\d.]+', line_stripped)
            if product_match:
                product_candidate = product_match.group(1).strip()
                cleaned_candidate = clean_product_title(product_candidate)
                if is_valid_product_title(cleaned_candidate) and len(cleaned_candidate) > 5:
                    return cleaned_candidate

    return ""

def extract_zip_files(folder_path):
    """Extract all zip files in the specified folder"""
    extracted_files = []
    print("\n" + "-"*80)
    print("📋 EXTRACT_ZIP_FILES FUNCTION STARTED")
    print("-"*80)
    temp_folder = os.path.join(folder_path, "extracted")
    if not os.path.exists(temp_folder):
        os.makedirs(temp_folder)
        print(f"📁 Created extraction folder: {temp_folder}")
    else:
        print(f"📁 Extraction folder already exists: {temp_folder}")
    zip_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.zip')]
    print(f"🔍 Found {len(zip_files)} zip file(s) in {folder_path}")

    for file in zip_files:
        zip_path = os.path.join(folder_path, file)
        zip_name = os.path.splitext(file)[0]
        extract_subfolder = os.path.join(temp_folder, zip_name)

        print(f"\n📦 Processing zip file: {file}")
        if os.path.exists(extract_subfolder) and os.listdir(extract_subfolder):
            print(f"   ⚠️ Zip file {file} already extracted")
            pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
            for extracted_file in pdf_files_in_subfolder:
                extracted_path = os.path.join(extract_subfolder, extracted_file)
                extracted_files.append(extracted_path)
            continue

        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                if not os.path.exists(extract_subfolder):
                    os.makedirs(extract_subfolder)
                zip_ref.extractall(extract_subfolder)

                pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
                for extracted_file in pdf_files_in_subfolder:
                    extracted_path = os.path.join(extract_subfolder, extracted_file)
                    extracted_files.append(extracted_path)

            print(f"   ✅ Successfully extracted {file}")
        except Exception as e:
            print(f"   ❌ Error extracting {file}: {str(e)}")

    return extracted_files

def send_data_to_api(json_file_path, api_url=None):
    """Send the generated JSON data to the specified API endpoint"""
    if not api_url:
        api_url = DEFAULT_API_URL

    if not api_url:
        print("⚠️ No API URL provided, skipping API upload")
        return False

    print(f"\n🌐 Sending data to API: {api_url}")

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        print(f"📄 Loaded JSON data from: {json_file_path}")
        invoices_array = []
        if "invoice" in json_data:
            for pdf_key, pages_data in json_data["invoice"].items():
                for page_data in pages_data:
                    if "IMEI/Serial Numbers" in page_data:
                        imei_data = page_data["IMEI/Serial Numbers"]
                        if isinstance(imei_data, list):
                            if imei_data:
                                page_data["IMEI_Serial_Numbers"] = ", ".join(str(imei) for imei in imei_data)
                                page_data["IMEI"] = ", ".join(str(imei) for imei in imei_data)
                            else:
                                page_data["IMEI_Serial_Numbers"] = ""
                                page_data["IMEI"] = ""
                        else:
                            page_data["IMEI_Serial_Numbers"] = str(imei_data) if imei_data else ""
                            page_data["IMEI"] = str(imei_data) if imei_data else ""
                    else:
                        page_data["IMEI_Serial_Numbers"] = ""
                        page_data["IMEI"] = ""
                    invoices_array.append(page_data)

        api_data = {
            "timestamp": json_data.get("timestamp", ""),
            "processing_date": json_data.get("processing_date", ""),
            "total_pdfs_processed": json_data.get("total_pdfs_processed", 0),
            "total_zip_files_extracted": json_data.get("total_zip_files_extracted", 0),
            "total_pdfs_from_zip": json_data.get("total_pdfs_from_zip", 0),
            "invoices_folder": json_data.get("invoices_folder", ""),
            "invoice": json_data.get("invoice", {}),
            "invoices": invoices_array
        }

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'PDF-Extractor/1.0'
        }

        response = requests.post(api_url, json=api_data, headers=headers, timeout=DEFAULT_API_TIMEOUT)

        if response.status_code == 200:
            print("✅ Data successfully sent to API!")
            return True
        else:
            print(f"❌ API request failed with status code: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error sending data to API: {str(e)}")
        return False

def extract_perfect_data(folder_path=None, api_url=None, api_key=None):
    """Extract data perfectly matching the actual PDF structure from specified folder"""
    print("="*80)
    print("🎯 ENHANCED PDF EXTRACTOR WITH TABULA-PY")
    print("="*80)
    print("Extracting data with enhanced product name detection...")

    if folder_path is None:
        invoice_folder = "Invoice"
    else:
        invoice_folder = folder_path
    if not os.path.exists(invoice_folder):
        print(f"❌ Error: Folder '{invoice_folder}' does not exist!")
        return None

    print(f"📁 Reading PDFs from folder: {invoice_folder}")

    results = {
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "processing_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_pdfs_processed": 0,
        "total_zip_files_extracted": 0,
        "total_pdfs_from_zip": 0,
        "invoices_folder": invoice_folder,
        "invoice": {}
    }

    # Extract zip files if any
    print(f"🔍 Checking for zip files in folder: {invoice_folder}")
    extracted_pdf_files = extract_zip_files(invoice_folder)
    pdf_files = []

    try:
        for file in os.listdir(invoice_folder):
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(invoice_folder, file))
        pdf_files.extend(extracted_pdf_files)

    except Exception as e:
        print(f"❌ Error reading folder '{invoice_folder}': {str(e)}")
        return None

    if not pdf_files:
        print(f"❌ No PDF files found in folder '{invoice_folder}' (including extracted zip files)")
        return None

    print(f"📄 Found {len(pdf_files)} PDF file(s) to process (including {len(extracted_pdf_files)} from zip files)")
    results["total_pdfs_from_zip"] = len(extracted_pdf_files)

    temp_folder = os.path.join(invoice_folder, "extracted")
    if os.path.exists(temp_folder):
        zip_subfolders = [d for d in os.listdir(temp_folder) if os.path.isdir(os.path.join(temp_folder, d))]
        results["total_zip_files_extracted"] = len(zip_subfolders)

    for pdf_path in pdf_files:
        print(f"\n🔍 Processing: {os.path.basename(pdf_path)}")

        try:
            pdf_data = extract_pdf_enhanced(pdf_path)
            if pdf_data:
                pdf_key = f"__{os.path.basename(pdf_path).replace('.pdf', '_pdf')}"
                results["invoice"][pdf_key] = pdf_data
                results["total_pdfs_processed"] += 1
                for page in pdf_data:
                    product = page.get('Product Title', 'N/A')
                    gross = page.get('Gross Amount', 'N/A')
                    total = page.get('Grand Total', 'N/A')
                    print(f"✅ Page {page['Page']}: Product='{product}', Gross={gross}, Total={total}")
        except Exception as e:
            print(f"❌ Error processing {os.path.basename(pdf_path)}: {str(e)}")

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"enhanced_extracted_data_{timestamp}.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=4, ensure_ascii=False)

    print(f"\n🎉 Enhanced extraction complete!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Total PDFs processed: {results['total_pdfs_processed']}")
    print(f"📦 Total ZIP files extracted: {results['total_zip_files_extracted']}")
    print(f"📄 Total PDFs from ZIP files: {results['total_pdfs_from_zip']}")

    # Send data to API
    api_success = send_data_to_api(output_file, api_url)
    if api_success:
        print("🌐 ✅ Data successfully sent to API!")
    else:
        print("🌐 ⚠️ Failed to send data to API (data still saved locally)")

    return output_file

def extract_pdf_enhanced(pdf_path):
    """Enhanced PDF extraction with tabula-py integration"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            pages_data = []

            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                if not text:
                    continue

                print(f"   📄 Extracting page {page_num + 1}...")
                data = {
                    "Page": page_num + 1,
                    "Customer Name": "",
                    "Shipping Address": "",
                    "Billing Address": "",
                    "Order ID": "",
                    "Order Date": "",
                    "Invoice Date": "",
                    "Invoice Number": "",
                    "PAN": "",
                    "CIN": "",
                    "GSTIN": "",
                    "IRN": "",
                    "Sold By": "",
                    "Product Title": "",
                    "Quantity": "",
                    "Gross Amount": "",
                    "Discount": "",
                    "Taxable Value": "",
                    "IGST": "",
                    "SGST": "",
                    "Grand Total": "",
                    "HSN/SAC": "",
                    "IMEI/Serial Numbers": [],
                    "Source PDF": os.path.basename(pdf_path).replace('.pdf', ''),
                    "Source Folder": os.path.dirname(pdf_path),
                    "Full Path": pdf_path
                }

                # Extract all fields using enhanced methods
                extract_enhanced_fields(text, data, pdf_path, page_num + 1)
                pages_data.append(data)

            return pages_data

    except Exception as e:
        print(f"   ❌ Error reading PDF: {str(e)}")
        return None

def extract_enhanced_fields(text, data, pdf_path, page_num):
    """Extract fields with enhanced product title detection"""

    # Enhanced product title extraction using tabula-py
    product_title = extract_product_title_enhanced(text, pdf_path, page_num)
    if product_title:
        data["Product Title"] = product_title
        print(f"   ✅ Product: {product_title}")

    # Use original extraction methods for other fields
    # (Import the functions from your original last_final.py)

    # Order ID
    order_match = re.search(r'Order\s+(?:Id|ID)[:\s]*\n?\s*(OD\d+)', text, re.IGNORECASE)
    if order_match:
        data["Order ID"] = order_match.group(1)
        print(f"   ✅ Order ID: {data['Order ID']}")

    # Invoice Number
    invoice_patterns = [
        r'Invoice\s+(?:No|Number)[:\s]*\n?\s*([A-Z0-9]+?)(?:Tax|$)',
        r'Invoice\s+Number\s*#\s*([A-Z0-9]+?)(?:Tax|$)',
        r'#\s*([A-Z0-9]{10,}?)(?:Tax|$)',
    ]

    for pattern in invoice_patterns:
        invoice_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_match:
            invoice_number = invoice_match.group(1).strip()
            invoice_number = re.sub(r'Tax$', '', invoice_number, flags=re.IGNORECASE)
            data["Invoice Number"] = invoice_number
            print(f"   ✅ Invoice Number: {data['Invoice Number']}")
            break

    # Extract other fields using simplified patterns
    # (You can add more extraction functions from your original file here)

    # GSTIN
    gstin_match = re.search(r'GSTIN[:\s]*([0-9]{2}[A-Z0-9]{10}[0-9][A-Z][A-Z0-9])', text, re.IGNORECASE)
    if gstin_match:
        data["GSTIN"] = gstin_match.group(1)
        print(f"   ✅ GSTIN: {data['GSTIN']}")

    # PAN
    pan_match = re.search(r'PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z]{1})', text, re.IGNORECASE)
    if pan_match:
        data["PAN"] = pan_match.group(1)
        print(f"   ✅ PAN: {data['PAN']}")

    # HSN/SAC
    print("Looking for HSN/SAC codes in text...")

    # Try multiple patterns to find HSN/SAC code
    hsn_sac_patterns = [
        # Standard pattern from the sample text
        r"HSN/SAC:\s*(\d+)",
        # Alternative patterns
        r"HSN/SAC[:\s]+(\d+)",
        r"HSN\s*Code[:\s]+(\d+)",
        r"SAC\s*Code[:\s]+(\d+)",
        r"HSN[:\s]+(\d+)",
        r"SAC[:\s]+(\d+)",
        # Patterns with colon
        r"HSN/SAC\s*:\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"SAC\s*:\s*(\d+)",
        # Patterns with dash
        r"HSN/SAC\s*-\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"SAC\s*-\s*(\d+)",
        # Patterns with HSN/SAC in table format
        r"HSN/SAC.*?\n.*?(\d+)",
        r"HSN.*?\n.*?(\d+)",
        r"SAC.*?\n.*?(\d+)",
        # Pattern for HSN/SAC followed by digits without space (common in invoices)
        r"HSN/SAC:(\d+)",
        r"HSN/SAC\s*:\s*(\d+)(?:[A-Z]|\s)",
        # Pattern for FSN followed by HSN/SAC
        r"FSN:.*?HSN/SAC:\s*(\d+)",
        # Pattern for HSN/SAC in product description
        r"HSN/SAC:\s*(\d+)[^\d\s]",
        # Patterns for HSN/SAC in product tables
        r"Product Title.*?HSN/SAC:?\s*(\d+)",
        r"Description.*?HSN/SAC:?\s*(\d+)",
        r"Item.*?HSN/SAC:?\s*(\d+)",
        # Pattern for HSN/SAC in a table cell
        r"HSN/SAC\s*\n\s*(\d+)",
        # Pattern for HSN/SAC code followed by percentage (common in invoices)
        r"HSN/SAC:?\s*(\d+).*?(\d+\.?\d*)\s*%",
        # Very generic pattern for any number after HSN/SAC
        r"HSN/SAC.*?(\d{4,10})",
        # Pattern for HSN code in brackets
        r"HSN\s*\(\s*(\d+)\s*\)",
        # Pattern for HSN code with slash
        r"HSN\s*/\s*(\d+)",
        # Pattern for HSN code with equals
        r"HSN\s*=\s*(\d+)",
        # Pattern for HSN code in a specific format
        r"HSN\s*:\s*(\d+)",
        # Pattern for HSN code in a table
        r"HSN\s*\|\s*(\d+)",
        # Pattern for HSN code in a list
        r"HSN\s*-\s*(\d+)",
        # Pattern for HSN code with no space
        r"HSN:(\d+)",
        # Pattern for HSN code with space
        r"HSN\s+(\d+)",
        # More generic pattern
        r"HSN.*?(\d{4,10})"
    ]

    for pattern in hsn_sac_patterns:
        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
        if hsn_sac_match:
            hsn_sac_code = hsn_sac_match.group(1).strip()
            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
            return hsn_sac_code

# Main execution
if __name__ == "__main__":
    import sys

    folder_path = sys.argv[1] if len(sys.argv) > 1 else None
    api_url = sys.argv[2] if len(sys.argv) > 2 else None

    result = extract_perfect_data(folder_path, api_url)
    if result:
        print(f"\n🎉 Processing completed successfully!")
        print(f"📁 Output file: {result}")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)