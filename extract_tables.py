import tabula
import os
import json
import re
import pandas as pd

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

# Path to your PDF file
pdf_path = "Invoice/OD330725585114150100.pdf"

# Directory to store JSON files
output_dir = "output_json"
os.makedirs(output_dir, exist_ok=True)

def extract_invoice_data(tables, page_num, pdf_filename):
    """Extract specific invoice information from tables"""
    invoice_data = {
        "Page": page_num,
        "Customer Name": "",
        "Shipping Address": "",
        "Billing Address": "",
        "Order ID": "",
        "Order Date": "",
        "Invoice Date": "",
        "Invoice Number": "",
        "PAN": "",
        "CIN": "",
        "GSTIN": "",
        "IRN": "",
        "Sold By": "",
        "Product Title": "",
        "Quantity": 0,
        "Taxable Value": 0.0,
        "IGST": 0.0,
        "Grand Total": 0.0,
        "HSN/SAC": "",
        "IMEI/Serial Numbers": [],
        "Source PDF": pdf_filename.replace('.pdf', ''),
        "Source Folder": ".\\Invoice",
        "Full Path": f".\\Invoice\\{pdf_filename}"
    }

    # Combine all table data into one text for easier parsing
    all_text = ""
    for df in tables:
        if not df.empty:
            # Convert dataframe to string and add to all_text
            for col in df.columns:
                for val in df[col].dropna():
                    if pd.notna(val):
                        all_text += str(val) + " "

    # Extract information using regex patterns
    patterns = {
        "Order ID": r"(?:Order\s*ID|Order\s*No|Order\s*Number|OD)[\s:]*([A-Z0-9]{10,})",
        "Invoice Number": r"(?:Invoice\s*No|Invoice\s*Number|Invoice\s*#)[\s:]*([A-Z0-9]{8,})",
        "Invoice Date": r"(?:Invoice\s*Date|Date\s*of\s*Invoice)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{4})",
        "Order Date": r"(?:Order\s*Date|Date\s*of\s*Order)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{4})",
        "PAN": r"(?:PAN|P\.A\.N)[\s:]*([A-Z0-9]{10})",
        "GSTIN": r"(?:GSTIN|GST\s*IN|GST\s*No)[\s:]*([A-Z0-9]{15})",
        "HSN/SAC": r"(?:HSN|SAC)[\s:]*(\d{6,8})",
        "Sold By": r"(?:Sold\s*By|Seller)[\s:]*([A-Z\s&\.\-]+?)(?:\s*\n|\s*,|$)",
    }

    # Extract using patterns
    for field, pattern in patterns.items():
        match = re.search(pattern, all_text, re.IGNORECASE)
        if match:
            invoice_data[field] = match.group(1).strip()

    # Extract customer name (usually appears after "Bill To" or "Ship To")
    customer_patterns = [
        r"(?:Bill\s*To|Ship\s*To)[\s:]*([A-Za-z\s,\.]+?)(?:\s*,|\s*\n|\s*\d|$)",
        r"(?:Customer\s*Name|Name)[\s:]*([A-Za-z\s,\.]+?)(?:\s*,|\s*\n|$)",
        r"(?:Billing\s*Name|Shipping\s*Name)[\s:]*([A-Za-z\s,\.]+?)(?:\s*,|\s*\n|$)"
    ]

    for pattern in customer_patterns:
        customer_match = re.search(pattern, all_text, re.IGNORECASE)
        if customer_match and len(customer_match.group(1).strip()) > 2:
            invoice_data["Customer Name"] = customer_match.group(1).strip()
            break

    # Extract addresses (more comprehensive patterns)
    address_patterns = [
        r"(?:Bill\s*To|Ship\s*To)[\s:]*[A-Za-z\s,\.]*?([A-Za-z0-9\s,\-\(\)\.\/]+(?:Building|Road|Street|Avenue|Lane|Apartment|Flat|Block|Society|Complex|Tower|Plot|House)[A-Za-z0-9\s,\-\(\)\.\/]*)",
        r"([A-Za-z0-9\s,\-\(\)\.\/]+(?:Building|Road|Street|Avenue|Lane|Apartment|Flat|Block|Society|Complex|Tower|Plot|House)[A-Za-z0-9\s,\-\(\)\.\/]*)",
        r"([A-Za-z0-9\s,\-\(\)\.\/]+\d{6}[A-Za-z0-9\s,\-\(\)\.\/]*)"  # Pattern with pincode
    ]

    for pattern in address_patterns:
        address_match = re.search(pattern, all_text, re.IGNORECASE)
        if address_match and len(address_match.group(1).strip()) > 10:
            address = address_match.group(1).strip()
            invoice_data["Shipping Address"] = address
            invoice_data["Billing Address"] = address
            break

    # Extract numerical values
    amount_patterns = {
        "Taxable Value": r"(?:Taxable\s*Value|Taxable\s*Amount)[\s:]*(\d+\.?\d*)",
        "IGST": r"(?:IGST)[\s:]*(\d+\.?\d*)",
        "Grand Total": r"(?:Grand\s*Total|Total\s*Amount)[\s:]*(\d+\.?\d*)",
        "Quantity": r"(?:Qty|Quantity)[\s:]*(\d+)"
    }

    for field, pattern in amount_patterns.items():
        match = re.search(pattern, all_text, re.IGNORECASE)
        if match:
            try:
                if field == "Quantity":
                    invoice_data[field] = int(match.group(1))
                else:
                    invoice_data[field] = float(match.group(1))
            except ValueError:
                pass

    # Extract product title (usually the longest text string)
    product_lines = re.findall(r"([A-Za-z][A-Za-z\s\-\(\)0-9]{20,})", all_text)
    if product_lines:
        # Take the longest line as product title
        invoice_data["Product Title"] = max(product_lines, key=len).strip()

    return invoice_data

# Get number of pages
from PyPDF2 import PdfReader
pdf = PdfReader(pdf_path)
num_pages = len(pdf.pages)

# Get PDF filename without path
pdf_filename = os.path.basename(pdf_path)

# Store all invoice data
all_invoice_data = []

# Extract and save table from each page
for page in range(1, num_pages + 1):
    try:
        # Try to extract all areas of the page to capture header information
        all_areas = []

        # Try lattice method first
        tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, lattice=True)
        all_areas.extend(tables)

        # If no tables found with lattice, try stream method
        if not tables or all(df.empty for df in tables):
            print(f"📝 Page {page}: No tables found with lattice method, trying stream method...")
            tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, stream=True)
            all_areas.extend(tables)

        # If still no tables, try guess method
        if not tables or all(df.empty for df in tables):
            print(f"📝 Page {page}: No tables found with stream method, trying guess method...")
            tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, guess=True)
            all_areas.extend(tables)

        # Try to extract entire page as one table to capture header info
        try:
            entire_page = tabula.read_pdf(pdf_path, pages=page, pandas_options={'header': None})
            if entire_page and not entire_page[0].empty:
                all_areas.extend(entire_page)
        except:
            pass

        # Try to extract all text areas without table structure
        try:
            text_areas = tabula.read_pdf(pdf_path, pages=page, area=[0, 0, 1000, 1000],
                                       relative_area=False, pandas_options={'header': None})
            if text_areas:
                all_areas.extend(text_areas)
        except:
            pass

        # Extract structured invoice data using all areas
        invoice_data = extract_invoice_data(all_areas, page, pdf_filename)
        all_invoice_data.append(invoice_data)

        # Also save raw table data for reference
        page_tables = []
        tables_found = 0

        for i, df in enumerate(tables):
            if not df.empty:
                page_tables.append(df.to_dict(orient='records'))
                tables_found += 1

        # Save raw JSON for this page
        with open(f"{output_dir}/page_{page}_raw.json", "w", encoding="utf-8") as f:
            json.dump(page_tables, f, indent=4, ensure_ascii=False)

        # Save structured invoice data for this page
        with open(f"{output_dir}/page_{page}_invoice.json", "w", encoding="utf-8") as f:
            json.dump(invoice_data, f, indent=4, ensure_ascii=False)

        if tables_found > 0:
            print(f"✅ Page {page}: Extracted {tables_found} table(s) and invoice data")
        else:
            print(f"⚠️ Page {page}: No tables detected, saved empty data")

    except Exception as e:
        print(f"❌ Error processing page {page}: {e}")

# Save consolidated invoice data
consolidated_data = {
    "invoice": {
        f"__{pdf_filename.replace('.pdf', '')}_pdf": all_invoice_data
    }
}

with open(f"{output_dir}/consolidated_invoice_data.json", "w", encoding="utf-8") as f:
    json.dump(consolidated_data, f, indent=4, ensure_ascii=False)

print(f"\n🎉 Processing complete! Consolidated data saved to {output_dir}/consolidated_invoice_data.json")
