import tabula
import os
import json

# Set JAVA_HOME for tabula-py
os.environ['JAVA_HOME'] = os.path.join(os.getcwd(), 'jdk-11.0.2')
os.environ['PATH'] = os.path.join(os.getcwd(), 'jdk-11.0.2', 'bin') + os.pathsep + os.environ.get('PATH', '')

# Path to your PDF file
pdf_path = "OD334338797558625100.pdf"

# Directory to store JSON files
output_dir = "output_json"
os.makedirs(output_dir, exist_ok=True)

# Get number of pages
from PyPDF2 import PdfReader
pdf = PdfReader(pdf_path)
num_pages = len(pdf.pages)

# Extract and save table from each page
for page in range(1, num_pages + 1):
    try:
        # Try lattice method first
        tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, lattice=True)

        # If no tables found with lattice, try stream method
        if not tables or all(df.empty for df in tables):
            print(f"📝 Page {page}: No tables found with lattice method, trying stream method...")
            tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, stream=True)

        # If still no tables, try guess method
        if not tables or all(df.empty for df in tables):
            print(f"📝 Page {page}: No tables found with stream method, trying guess method...")
            tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, guess=True)

        page_tables = []
        tables_found = 0

        for i, df in enumerate(tables):
            if not df.empty:
                page_tables.append(df.to_dict(orient='records'))
                tables_found += 1

        # Save JSON for this page
        with open(f"{output_dir}/page_{page}.json", "w", encoding="utf-8") as f:
            json.dump(page_tables, f, indent=4, ensure_ascii=False)

        if tables_found > 0:
            print(f"✅ Page {page}: Extracted {tables_found} table(s) and saved to JSON")
        else:
            print(f"⚠️ Page {page}: No tables detected, saved empty JSON")

    except Exception as e:
        print(f"❌ Error processing page {page}: {e}")
