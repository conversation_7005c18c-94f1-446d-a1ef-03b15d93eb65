import tabula
import os
import json

# Path to your PDF file
pdf_path = "OD334338797558625100.pdf"

# Directory to store JSON files
output_dir = "output_json"
os.makedirs(output_dir, exist_ok=True)

# Get number of pages
from PyPDF2 import PdfReader
pdf = PdfReader(pdf_path)
num_pages = len(pdf.pages)

# Extract and save table from each page
for page in range(1, num_pages + 1):
    try:
        tables = tabula.read_pdf(pdf_path, pages=page, multiple_tables=True, lattice=True)
        page_tables = []

        for i, df in enumerate(tables):
            page_tables.append(df.to_dict(orient='records'))

        # Save JSON for this page
        with open(f"{output_dir}/page_{page}.json", "w", encoding="utf-8") as f:
            json.dump(page_tables, f, indent=4, ensure_ascii=False)

        print(f"✅ Page {page}: Extracted and saved to JSON")

    except Exception as e:
        print(f"❌ Error processing page {page}: {e}")
